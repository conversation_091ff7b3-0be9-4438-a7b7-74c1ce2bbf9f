# Create a self-signed code signing certificate
$cert = New-SelfSignedCertificate -Subject "CN=Adam Frangione" `
    -Type CodeSigningCert `
    -CertStoreLocation "Cert:\CurrentUser\My" `
    -KeyUsage DigitalSignature `
    -KeyLength 2048 `
    -NotAfter (Get-Date).AddYears(3)

# Export the certificate to a PFX file with a password
$password = ConvertTo-SecureString -String "PanAura2025" -Force -AsPlainText
Export-PfxCertificate -Cert $cert -FilePath "MyCodeSigningCert.pfx" -Password $password

# Add to Trusted Root store so your system trusts it
Export-Certificate -Cert $cert -FilePath "MyCodeSigningCert.cer"
Import-Certificate -FilePath "MyCodeSigningCert.cer" -CertStoreLocation "Cert:\CurrentUser\Root"