# Verify the signature on the PS1 file
$result = Get-AuthenticodeSignature -FilePath "CREATE - List of Files in Directory.ps1"

# Display the result in a more readable format
Write-Host "Signature Status: $($result.Status)"
Write-Host "Signer: $($result.SignerCertificate.Subject)"
Write-Host "Timestamp: $($result.TimeStamperCertificate)"
Write-Host "Path: $($result.Path)"

# Keep the console window open until user presses a key
Write-Host "`nPress any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
