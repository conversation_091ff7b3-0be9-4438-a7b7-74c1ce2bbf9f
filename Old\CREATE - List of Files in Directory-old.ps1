#Requires -Version 5.1
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName Microsoft.VisualBasic

$global:totalFiles = 0

# Show usage message
[System.Windows.Forms.MessageBox]::Show(
    "Usage Instructions:`n`n" +
    "• This script generates a list of all files in a selected folder." + "`n" +
    "• You will need to Provide a full path to folder for scanning." + "`n" +
    "• If you leave input blank it will default to current directory." + "`n" +
    "• You will also be asked if you want to include subfolders." + "`n`n" +
    "• The output will be saved as both a .txt and a .csv file." + "`n" +
    "• The list excludes this script and the output files it creates." + "`n" +
    "• After completion, the .txt file will automatically open." + "`n`n" +
    "Make sure to run this script in the folder where you want the output created.",
    "ADAMS - File Listing Script - Instructions",
    [System.Windows.Forms.MessageBoxButtons]::OK,
    [System.Windows.Forms.MessageBoxIcon]::Information
)

# Function to prompt for folder path with proper Cancel handling
function Get-FolderPathFromUser {
    $form = New-Object System.Windows.Forms.Form
    $form.Text = "Select Folder"
    $form.Width = 500
    $form.Height = 150
    $form.StartPosition = "CenterScreen"

    $label = New-Object System.Windows.Forms.Label
    $label.Text = "Enter the full path to the folder you want to scan (or leave blank to use the current directory):"
    $label.AutoSize = $true
    $label.Top = 10
    $label.Left = 10
    $form.Controls.Add($label)

    $textBox = New-Object System.Windows.Forms.TextBox
    $textBox.Width = 460
    $textBox.Top = 40
    $textBox.Left = 10
    $form.Controls.Add($textBox)

    $okButton = New-Object System.Windows.Forms.Button
    $okButton.Text = "OK"
    $okButton.DialogResult = [System.Windows.Forms.DialogResult]::OK
    $okButton.Top = 75
    $okButton.Left = 310
    $form.AcceptButton = $okButton
    $form.Controls.Add($okButton)

    $cancelButton = New-Object System.Windows.Forms.Button
    $cancelButton.Text = "Cancel"
    $cancelButton.DialogResult = [System.Windows.Forms.DialogResult]::Cancel
    $cancelButton.Top = 75
    $cancelButton.Left = 390
    $form.CancelButton = $cancelButton
    $form.Controls.Add($cancelButton)

    $dialogResult = $form.ShowDialog()

    if ($dialogResult -eq [System.Windows.Forms.DialogResult]::Cancel) {
        [System.Windows.Forms.MessageBox]::Show("Operation cancelled by user.", "Cancelled", "OK", "Information")
        exit
    }

    return $textBox.Text
}

# Prompt user for target folder
$targetFolder = Get-FolderPathFromUser
if ([string]::IsNullOrWhiteSpace($targetFolder)) {
    $targetFolder = Get-Location
}
if (-not (Test-Path $targetFolder)) {
    [System.Windows.Forms.MessageBox]::Show("The path you entered does not exist. Exiting script.", "Invalid Path", "OK", "Error")
    exit
}

# Ask to include subfolders
$includeSubfolders = [System.Windows.Forms.MessageBox]::Show("Include subfolders?", "Include Subfolders", [System.Windows.Forms.MessageBoxButtons]::YesNoCancel, [System.Windows.Forms.MessageBoxIcon]::Question)
if ($includeSubfolders -eq [System.Windows.Forms.DialogResult]::Cancel) {
    [System.Windows.Forms.MessageBox]::Show("Operation cancelled by user.", "Cancelled", "OK", "Information")
    exit
}

$outputFile = Join-Path $targetFolder "FileList.txt"
$csvFile = Join-Path $targetFolder "FileList.csv"
$currentScript = $MyInvocation.MyCommand.Path
if ($currentScript -like "*.exe") {
    $currentScript = [System.Diagnostics.Process]::GetCurrentProcess().MainModule.FileName
}

Remove-Item -Path $outputFile, $csvFile -ErrorAction SilentlyContinue

function List-Files {
    param (
        [string]$path,
        [int]$indent = 0
    )

    $folder = Get-Item -LiteralPath $path
    $line = '-' * 60
    Add-Content -Path $outputFile -Value $line
    Add-Content -Path $outputFile -Value (' ' * $indent + "Folder: " + $folder.FullName)

    $files = Get-ChildItem -Path $path -File | Where-Object {
        $_.FullName -ne $currentScript -and $_.FullName -ne $outputFile -and $_.FullName -ne $csvFile
    }

    Add-Content -Path $outputFile -Value (' ' * ($indent + 4) + "Number of files: $($files.Count)")
    Add-Content -Path $outputFile -Value ""

    foreach ($file in $files) {
        Add-Content -Path $outputFile -Value (' ' * ($indent + 4) + $file.Name)
        $global:totalFiles++
    }

    Add-Content -Path $outputFile -Value ""

    foreach ($file in $files) {
        [PSCustomObject]@{
            Folder = $path
            FileName = $file.Name
        } | Export-Csv -Path $csvFile -Append -NoTypeInformation -Encoding UTF8
    }

    if ($includeSubfolders -eq [System.Windows.Forms.DialogResult]::Yes) {
        Get-ChildItem -Path $path -Directory | ForEach-Object {
            List-Files -path $_.FullName -indent ($indent + 4)
        }
    }
}

List-Files -path $targetFolder

# Add total number of files to the top of the .txt file
$lines = Get-Content $outputFile
$lines = @("Total number of files: $global:totalFiles", "") + $lines
Set-Content -Path $outputFile -Value $lines

Start-Process notepad.exe $outputFile
