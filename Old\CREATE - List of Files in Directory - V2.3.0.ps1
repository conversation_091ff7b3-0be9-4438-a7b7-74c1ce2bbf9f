<#PSScriptInfo
.VERSION $pversion
.AUTHOR Adam Frangione
.DESCRIPTION Generates a list of all files in a selected folder
.TAGS files directory listing inventory
.COPYRIGHT Copyright (c) 2025, Adam Frangione
#>

#Requires -Version 5.1
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName Microsoft.VisualBasic

$pversion = "2.2.4"
$global:totalFiles = 0

# Add a progress indicator function
function Show-Progress {
    param (
        [string]$Activity,
        [int]$PercentComplete
    )
    
    Write-Progress -Activity $Activity -PercentComplete $PercentComplete
}

# Show usage message
[System.Windows.Forms.MessageBox]::Show(
    "Usage Instructions:`n`n" +
    "• This script generates a list of all files in a selected folder." + "`n" +
    "• You will need to Provide a full path to folder for scanning." + "`n" +
    "• If you leave input blank it will default to current directory." + "`n" +
    "• You will also be asked if you want to include subfolders." + "`n`n" +
    "• The output will be saved as both a .txt and a .csv file." + "`n" +
    "• The list excludes this script and the output files it creates." + "`n" +
    "• After completion, the .txt file will automatically open." + "`n`n" +
    "Make sure to run this script in the folder where you want the output created.",
    "ADAMS - File Listing Script - Instructions",
    [System.Windows.Forms.MessageBoxButtons]::OK,
    [System.Windows.Forms.MessageBoxIcon]::Information
)

# Function to prompt for folder path with proper Cancel handling and browse button
function Get-FolderPathFromUser {
    $form = New-Object System.Windows.Forms.Form
    $form.Text = "Select Folder"
    $form.Width = 500
    $form.Height = 150
    $form.StartPosition = "CenterScreen"

    $label = New-Object System.Windows.Forms.Label
    $label.Text = "Enter the full path to the folder you want to scan (or leave blank to use the current directory):"
    $label.AutoSize = $true
    $label.Top = 10
    $label.Left = 10
    $form.Controls.Add($label)

    $textBox = New-Object System.Windows.Forms.TextBox
    $textBox.Width = 370
    $textBox.Top = 40
    $textBox.Left = 10
    $form.Controls.Add($textBox)

    $browseButton = New-Object System.Windows.Forms.Button
    $browseButton.Text = "Browse..."
    $browseButton.Top = 39
    $browseButton.Left = 390
    $browseButton.Width = 80
    $form.Controls.Add($browseButton)

    $browseButton.Add_Click({
        $folderBrowser = New-Object System.Windows.Forms.FolderBrowserDialog
        $folderBrowser.Description = "Select a folder to scan"
        $folderBrowser.ShowNewFolderButton = $true
        
        if ($folderBrowser.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) {
            $textBox.Text = $folderBrowser.SelectedPath
        }
    })

    $okButton = New-Object System.Windows.Forms.Button
    $okButton.Text = "OK"
    $okButton.DialogResult = [System.Windows.Forms.DialogResult]::OK
    $okButton.Top = 75
    $okButton.Left = 310
    $form.AcceptButton = $okButton
    $form.Controls.Add($okButton)

    $cancelButton = New-Object System.Windows.Forms.Button
    $cancelButton.Text = "Cancel"
    $cancelButton.DialogResult = [System.Windows.Forms.DialogResult]::Cancel
    $cancelButton.Top = 75
    $cancelButton.Left = 390
    $form.CancelButton = $cancelButton
    $form.Controls.Add($cancelButton)

    $dialogResult = $form.ShowDialog()

    if ($dialogResult -eq [System.Windows.Forms.DialogResult]::Cancel) {
        [System.Windows.Forms.MessageBox]::Show("Operation cancelled by user.", "Cancelled", "OK", "Information")
        exit
    }

    return $textBox.Text
}

# Prompt user for target folder
$targetFolder = Get-FolderPathFromUser
if ([string]::IsNullOrWhiteSpace($targetFolder)) {
    $targetFolder = Get-Location
}
if (-not (Test-Path $targetFolder)) {
    [System.Windows.Forms.MessageBox]::Show("The path you entered does not exist. Exiting script.", "Invalid Path", "OK", "Error")
    exit
}

# Ask to include subfolders
$includeSubfolders = [System.Windows.Forms.MessageBox]::Show("Include subfolders?", "Include Subfolders", [System.Windows.Forms.MessageBoxButtons]::YesNoCancel, [System.Windows.Forms.MessageBoxIcon]::Question)
if ($includeSubfolders -eq [System.Windows.Forms.DialogResult]::Cancel) {
    [System.Windows.Forms.MessageBox]::Show("Operation cancelled by user.", "Cancelled", "OK", "Information")
    exit
}

$outputFile = Join-Path $targetFolder "FileList.txt"
$csvFile = Join-Path $targetFolder "FileList.csv"
$currentScript = $MyInvocation.MyCommand.Path
if ($currentScript -like "*.exe") {
    $currentScript = [System.Diagnostics.Process]::GetCurrentProcess().MainModule.FileName
}

Remove-Item -Path $outputFile, $csvFile -ErrorAction SilentlyContinue

function Get-Files {
    param (
        [string]$path,
        [int]$indent = 0
    )

    $folder = Get-Item -LiteralPath $path
    $line = '-' * 60
    Add-Content -Path $outputFile -Value $line
    Add-Content -Path $outputFile -Value (' ' * $indent + "Folder: " + $folder.FullName)

    $files = Get-ChildItem -Path $path -File | Where-Object {
        $_.FullName -ne $currentScript -and $_.FullName -ne $outputFile -and $_.FullName -ne $csvFile
    }

    Add-Content -Path $outputFile -Value (' ' * ($indent + 4) + "Number of files: $($files.Count)")
    Add-Content -Path $outputFile -Value ""

    foreach ($file in $files) {
        $fileInfo = "$('{0,-40}' -f $file.Name) | Size: $([math]::Round($file.Length/1KB, 2)) KB | Modified: $($file.LastWriteTime)"
        Add-Content -Path $outputFile -Value (' ' * ($indent + 4) + $fileInfo)
        $global:totalFiles++
    }

    Add-Content -Path $outputFile -Value ""

    foreach ($file in $files) {
        [PSCustomObject]@{
            Folder = $path
            FileName = $file.Name
            SizeKB = [math]::Round($file.Length/1KB, 2)
            LastModified = $file.LastWriteTime
            Extension = $file.Extension
        } | Export-Csv -Path $csvFile -Append -NoTypeInformation -Encoding UTF8
    }

    if ($includeSubfolders -eq [System.Windows.Forms.DialogResult]::Yes) {
        $subDirs = Get-ChildItem -Path $path -Directory
        $dirCount = $subDirs.Count
        $totalDirs = $dirCount + 1 # Add 1 to include the current directory in the count
        $currentDir = 0
        
        foreach ($dir in $subDirs) {
            $i++
            $currentDir++
            $percentComplete = [math]::Min(100, [math]::Round(($currentDir / $totalDirs) * 100))
            Show-Progress -Activity "Processing folders" -PercentComplete $percentComplete
            Get-Files -path $dir.FullName -indent ($indent + 4)
        }
    }
}

# Add error handling for file operations
try {
    Get-Files -path $targetFolder
    
    # Add total number of files to the top of the .txt file
    $lines = Get-Content $outputFile
    $lines = @("Total number of files: $global:totalFiles", "") + $lines
    Set-Content -Path $outputFile -Value $lines
    
    # Add a summary section to the output file
    function Add-Summary {
        param (
            [string]$outputFile
        )
        
        $allFiles = Import-Csv -Path $csvFile
        $totalSizeKB = ($allFiles | Measure-Object -Property SizeKB -Sum).Sum
        $totalSizeMB = [math]::Round($totalSizeKB / 1024, 2)
        $totalSizeGB = [math]::Round($totalSizeMB / 1024, 2)
        $fileTypes = $allFiles | Group-Object -Property Extension | Sort-Object -Property Count -Descending
        
        Add-Content -Path $outputFile -Value "`n`n$('-' * 60)"
        Add-Content -Path $outputFile -Value "SUMMARY"
        Add-Content -Path $outputFile -Value "$('-' * 60)"
        Add-Content -Path $outputFile -Value "Total Files: $global:totalFiles"
        Add-Content -Path $outputFile -Value "Total Size: $totalSizeKB KB ($totalSizeMB MB / $totalSizeGB GB)"
        Add-Content -Path $outputFile -Value "`nFile Types:"
        
        foreach ($type in $fileTypes) {
            # Calculate size per file type
            $typeSizeKB = ($allFiles | Where-Object Extension -eq $type.Name | Measure-Object -Property SizeKB -Sum).Sum
            $typeSizeMB = [math]::Round($typeSizeKB / 1024, 2)
            
            # Only show MB for file types with significant size
            if ($typeSizeMB -ge 1) {
                Add-Content -Path $outputFile -Value "  $($type.Name): $($type.Count) files - $typeSizeKB KB ($typeSizeMB MB)"
            } else {
                Add-Content -Path $outputFile -Value "  $($type.Name): $($type.Count) files - $typeSizeKB KB"
            }
        }
        
        # Add largest files section (top 10)
        Add-Content -Path $outputFile -Value "`nLargest Files (Top 10):"
        $largestFiles = $allFiles | Sort-Object -Property {[double]$_.SizeKB} -Descending | Select-Object -First 10
        foreach ($file in $largestFiles) {
            $fileSizeMB = [math]::Round([double]$file.SizeKB / 1024, 2)
            if ($fileSizeMB -ge 1) {
                Add-Content -Path $outputFile -Value "  $($file.FileName) - $([double]$file.SizeKB) KB ($fileSizeMB MB)"
            } else {
                Add-Content -Path $outputFile -Value "  $($file.FileName) - $([double]$file.SizeKB) KB"
            }
        }
    }

    # Call the summary function before opening the file
    Add-Summary -outputFile $outputFile

    Start-Process notepad.exe $outputFile
} catch {
    [System.Windows.Forms.MessageBox]::Show("Error: $_", "Error", "OK", "Error")
}
