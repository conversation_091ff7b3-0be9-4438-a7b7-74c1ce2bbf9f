# Load the certificate
$cert = Get-PfxCertificate -FilePath "MyCodeSigningCert.pfx"

# Sign the executable with timestamp
Set-AuthenticodeSignature -FilePath "CREATE - List of Files in Directory.exe" -Certificate $cert -TimestampServer "http://timestamp.digicert.com"

# Verify the signature
$result = Get-AuthenticodeSignature -FilePath "CREATE - List of Files in Directory.exe"

# Display the result
Write-Host "Signature Status: $($result.Status)"
Write-Host "Signer: $($result.SignerCertificate.Subject)"
Write-Host "Timestamp: $($result.TimeStamperCertificate)"

# Keep the console window open
Write-Host "`nPress any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")